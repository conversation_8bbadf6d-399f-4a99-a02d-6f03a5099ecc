import { NodeType } from "@/constants/enums";
import { NodeMetaData } from "@/types/workflow/node";

export const BASE_NODES: NodeMetaData[] = [
  {
    label: "开始",
    description: "开始节点",
    type: NodeType.START,
    icon: null,
  },
  {
    label: "LLM",
    description: "LLM节点",
    type: NodeType.LLM,
    icon: null,
  },
  {
    label: "结束",
    description: "结束节点",
    type: NodeType.END,
    icon: null,
  },
];
