import { NodeType } from "@/constants/enums";
import { NodeCategory, NodeCategoryConfig, NodeSelectItem } from "@/types/nodeSelectPanel";
import React from "react";

// 节点图标组件
const NodeIcons = {
  LLM: (
    <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-100 text-blue-600">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 2a6 6 0 100 12A6 6 0 008 2zM4 8a4 4 0 118 0 4 4 0 01-8 0z"/>
        <circle cx="8" cy="8" r="2"/>
      </svg>
    </div>
  ),
  KNOWLEDGE: (
    <div className="flex h-6 w-6 items-center justify-center rounded bg-purple-100 text-purple-600">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M3 2a1 1 0 00-1 1v10a1 1 0 001 1h10a1 1 0 001-1V3a1 1 0 00-1-1H3zm1 2h8v8H4V4z"/>
        <path d="M6 6h4v1H6V6zm0 2h4v1H6V8z"/>
      </svg>
    </div>
  ),
  END: (
    <div className="flex h-6 w-6 items-center justify-center rounded bg-green-100 text-green-600">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 2L3 7h3v6h4V7h3L8 2z"/>
      </svg>
    </div>
  ),
  CONDITION: (
    <div className="flex h-6 w-6 items-center justify-center rounded bg-red-100 text-red-600">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 1L4 5h2v6h4V5h2L8 1z"/>
        <path d="M2 11h12v2H2v-2z"/>
      </svg>
    </div>
  ),
  CUSTOM: (
    <div className="flex h-6 w-6 items-center justify-center rounded bg-gray-100 text-gray-600">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 2a6 6 0 100 12A6 6 0 008 2zM7 5h2v2H7V5zm0 4h2v4H7V9z"/>
      </svg>
    </div>
  ),
};

/**
 * 可选择的节点列表配置
 */
export const NODE_SELECT_ITEMS: NodeSelectItem[] = [
  // 无分类节点
  {
    id: "llm",
    name: "LLM",
    description: "大语言模型节点，用于文本生成和对话",
    type: NodeType.LLM,
    icon: NodeIcons.LLM,
    category: NodeCategory.NONE,
    enabled: true,
  },
  {
    id: "knowledge",
    name: "知识库检索",
    description: "从知识库中检索相关信息",
    type: NodeType.KNOWLEDGE,
    icon: NodeIcons.KNOWLEDGE,
    category: NodeCategory.NONE,
    enabled: true,
  },
  {
    id: "reply",
    name: "回复",
    description: "输出最终回复内容",
    type: NodeType.END,
    icon: NodeIcons.END,
    category: NodeCategory.NONE,
    enabled: true,
  },
  // 逻辑判断分类
  {
    id: "condition",
    name: "条件分支",
    description: "根据条件进行分支判断",
    type: NodeType.CONDITION,
    icon: NodeIcons.CONDITION,
    category: NodeCategory.LOGIC,
    enabled: true,
  },
  // 工具分类
  {
    id: "custom",
    name: "自定义组件",
    description: "自定义功能组件",
    type: NodeType.CUSTOM,
    icon: NodeIcons.CUSTOM,
    category: NodeCategory.TOOLS,
    enabled: true,
  },
];

/**
 * 节点分类配置
 */
export const NODE_CATEGORIES: NodeCategoryConfig[] = [
  {
    key: NodeCategory.NONE,
    label: "搜索节点",
    showTitle: false, // 无分类节点不显示标题
    nodes: NODE_SELECT_ITEMS.filter(item => item.category === NodeCategory.NONE),
  },
  {
    key: NodeCategory.LOGIC,
    label: "逻辑判断",
    showTitle: true,
    nodes: NODE_SELECT_ITEMS.filter(item => item.category === NodeCategory.LOGIC),
  },
  {
    key: NodeCategory.TOOLS,
    label: "工具",
    showTitle: true,
    nodes: NODE_SELECT_ITEMS.filter(item => item.category === NodeCategory.TOOLS),
  },
];

/**
 * 搜索框的随机placeholder文案
 */
export const SEARCH_PLACEHOLDERS = [
  "搜索节点...",
  "输入节点名称...",
  "查找所需节点...",
  "搜索功能组件...",
  "输入关键词搜索...",
];

/**
 * 获取随机placeholder文案
 */
export const getRandomPlaceholder = (): string => {
  const randomIndex = Math.floor(Math.random() * SEARCH_PLACEHOLDERS.length);
  return SEARCH_PLACEHOLDERS[randomIndex];
};

/**
 * 根据关键词过滤节点
 */
export const filterNodesByKeyword = (keyword: string): NodeSelectItem[] => {
  if (!keyword.trim()) {
    return NODE_SELECT_ITEMS;
  }
  
  const lowerKeyword = keyword.toLowerCase().trim();
  return NODE_SELECT_ITEMS.filter(item => 
    item.name.toLowerCase().includes(lowerKeyword) ||
    item.description.toLowerCase().includes(lowerKeyword)
  );
};

/**
 * 根据过滤后的节点重新组织分类
 */
export const getCategoriesWithFilteredNodes = (filteredNodes: NodeSelectItem[]): NodeCategoryConfig[] => {
  return NODE_CATEGORIES.map(category => ({
    ...category,
    nodes: filteredNodes.filter(item => item.category === category.key),
  })).filter(category => category.nodes.length > 0);
};
