import { useCallback } from "react";
import { useReactFlow } from "@xyflow/react";
import { NodeAddPosition } from "@/types/nodeSelectPanel";

/**
 * 节点位置计算hook
 */
export function useNodePosition() {
  const reactFlowInstance = useReactFlow();

  /**
   * 获取画布中心位置
   */
  const getCenterPosition = useCallback((): NodeAddPosition => {
    if (!reactFlowInstance) {
      return { x: 400, y: 300 };
    }

    const viewport = reactFlowInstance.getViewport();
    const bounds = reactFlowInstance.getViewport();
    
    // 获取画布的可视区域中心
    const centerX = -viewport.x / viewport.zoom + (window.innerWidth / 2) / viewport.zoom;
    const centerY = -viewport.y / viewport.zoom + (window.innerHeight / 2) / viewport.zoom;

    return { x: centerX, y: centerY };
  }, [reactFlowInstance]);

  /**
   * 获取画布右侧位置（用于点击添加）
   */
  const getRightSidePosition = useCallback((): NodeAddPosition => {
    if (!reactFlowInstance) {
      return { x: 600, y: 300 };
    }

    const viewport = reactFlowInstance.getViewport();
    const nodes = reactFlowInstance.getNodes();
    
    // 找到最右侧的节点
    let maxX = 0;
    nodes.forEach(node => {
      const nodeRight = node.position.x + (node.width || 240); // 默认节点宽度240px
      if (nodeRight > maxX) {
        maxX = nodeRight;
      }
    });

    // 在最右侧节点右边留出间距
    const rightX = maxX + 300; // 300px间距
    const centerY = -viewport.y / viewport.zoom + (window.innerHeight / 2) / viewport.zoom;

    return { x: rightX, y: centerY };
  }, [reactFlowInstance]);

  /**
   * 获取指定位置附近的空闲位置
   */
  const getNearbyFreePosition = useCallback((
    targetPosition: NodeAddPosition,
    nodeWidth = 240,
    nodeHeight = 100
  ): NodeAddPosition => {
    if (!reactFlowInstance) {
      return targetPosition;
    }

    const nodes = reactFlowInstance.getNodes();
    const spacing = 50; // 节点间距

    // 检查位置是否被占用
    const isPositionOccupied = (x: number, y: number): boolean => {
      return nodes.some(node => {
        const nodeX = node.position.x;
        const nodeY = node.position.y;
        const nodeW = node.width || 240;
        const nodeH = node.height || 100;

        return (
          x < nodeX + nodeW + spacing &&
          x + nodeWidth + spacing > nodeX &&
          y < nodeY + nodeH + spacing &&
          y + nodeHeight + spacing > nodeY
        );
      });
    };

    let { x, y } = targetPosition;

    // 如果目标位置被占用，尝试找到附近的空闲位置
    if (isPositionOccupied(x, y)) {
      const maxAttempts = 20;
      let attempts = 0;
      let found = false;

      // 螺旋式搜索空闲位置
      for (let radius = nodeWidth + spacing; radius <= 1000 && !found && attempts < maxAttempts; radius += nodeWidth + spacing) {
        const positions = [
          { x: x + radius, y }, // 右
          { x: x - radius, y }, // 左
          { x, y: y + radius }, // 下
          { x, y: y - radius }, // 上
          { x: x + radius, y: y + radius }, // 右下
          { x: x - radius, y: y + radius }, // 左下
          { x: x + radius, y: y - radius }, // 右上
          { x: x - radius, y: y - radius }, // 左上
        ];

        for (const pos of positions) {
          if (!isPositionOccupied(pos.x, pos.y)) {
            x = pos.x;
            y = pos.y;
            found = true;
            break;
          }
        }
        attempts++;
      }
    }

    return { x, y };
  }, [reactFlowInstance]);

  /**
   * 屏幕坐标转换为画布坐标
   */
  const screenToFlowPosition = useCallback((screenX: number, screenY: number): NodeAddPosition => {
    if (!reactFlowInstance) {
      return { x: screenX, y: screenY };
    }

    const position = reactFlowInstance.screenToFlowPosition({ x: screenX, y: screenY });
    return { x: position.x, y: position.y };
  }, [reactFlowInstance]);

  return {
    getCenterPosition,
    getRightSidePosition,
    getNearbyFreePosition,
    screenToFlowPosition,
  };
}
