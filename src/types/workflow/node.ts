import { NodeType } from "@/constants/enums";
import React from "react";
import { Variable } from "./common";

export interface NodeMetaData {
  label: string;
  description: string;
  type: NodeType;
  icon: React.ReactNode | null;
}

export interface BaseNodeData extends Record<string, unknown> {
  label: string;
  description?: string;
}

export interface StartNodeData extends BaseNodeData {
  variables?: Variable[];
}

export interface LLMNodeData extends BaseNodeData {
  model?: {
    provider: string;
    model_name: string;
    model_config: { temperature: number };
  };
  system_prompt?: string;
  user_prompt?: string;
}

export interface EndNodeData extends BaseNodeData {
  answer?: string;
}

export type NodeData = StartNodeData | LLMNodeData | EndNodeData;

export interface BuiltinNode {
  id: string;
  label: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
  description?: string;
}
