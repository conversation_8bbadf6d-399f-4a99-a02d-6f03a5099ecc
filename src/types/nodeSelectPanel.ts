import { NodeType } from "@/constants/enums";
import React from "react";

/**
 * 节点选择面板中的节点项数据结构
 */
export interface NodeSelectItem {
  /** 节点唯一标识 */
  id: string;
  /** 节点显示名称 */
  name: string;
  /** 节点描述 */
  description: string;
  /** 节点类型 */
  type: NodeType;
  /** 节点图标 */
  icon?: React.ReactNode;
  /** 节点分类 */
  category?: NodeCategory;
  /** 是否可用 */
  enabled?: boolean;
}

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  /** 无分类（直接展示） */
  NONE = "none",
  /** 逻辑判断 */
  LOGIC = "logic", 
  /** 工具 */
  TOOLS = "tools",
}

/**
 * 节点分类配置
 */
export interface NodeCategoryConfig {
  /** 分类标识 */
  key: NodeCategory;
  /** 分类显示名称 */
  label: string;
  /** 是否显示分类标题 */
  showTitle: boolean;
  /** 分类下的节点列表 */
  nodes: NodeSelectItem[];
}

/**
 * 搜索过滤状态
 */
export interface SearchState {
  /** 搜索关键词 */
  keyword: string;
  /** 过滤后的节点列表 */
  filteredNodes: NodeSelectItem[];
  /** 是否有搜索结果 */
  hasResults: boolean;
}

/**
 * 节点添加位置信息
 */
export interface NodeAddPosition {
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
}

/**
 * 节点选择面板的Props
 */
export interface NodeSelectPanelProps {
  /** 面板是否可见 */
  visible?: boolean;
  /** 面板关闭回调 */
  onClose?: () => void;
  /** 节点点击添加回调 */
  onNodeClick?: (node: NodeSelectItem, position?: NodeAddPosition) => void;
  /** 节点拖拽开始回调 */
  onNodeDragStart?: (node: NodeSelectItem, event: React.DragEvent) => void;
  /** 节点拖拽结束回调 */
  onNodeDragEnd?: (node: NodeSelectItem, position: NodeAddPosition) => void;
}

/**
 * 拖拽状态
 */
export interface DragState {
  /** 是否正在拖拽 */
  isDragging: boolean;
  /** 拖拽的节点 */
  dragNode: NodeSelectItem | null;
  /** 拖拽预览位置 */
  previewPosition: NodeAddPosition | null;
}
