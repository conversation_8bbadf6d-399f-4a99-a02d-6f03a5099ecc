import {
  addEdge,
  applyEdgeChanges,
  applyNodeChang<PERSON>,
  Background,
  Connection,
  Edge,
  EdgeChange,
  Node,
  NodeChange,
  ReactFlow,
} from "@xyflow/react";
import React, { useCallback, useEffect, useState } from "react";

import { useWorkflowStore } from "@/stores/workflowStore";
import CanvasOverlay from "./canvasOverlay";
import GenericNode from "./node";

const nodeTypes = {
  genericNode: GenericNode,
};

export default function Workbench() {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);

  const setSelectedNodeId = useWorkflowStore(
    (state) => state.setSelectedNodeId,
  );
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);

  useEffect(() => {
    if (currentWorkflow) {
      setNodes(
        currentWorkflow.nodes.map((node) => ({
          id: node.id,
          position: node.position,
          data: node.data,
          type: "genericNode",
        })),
      );
      setEdges(currentWorkflow.edges);
    }
  }, [currentWorkflow]);

  const onNodesChange = useCallback(
    (changes: NodeChange[]) =>
      setNodes((nodesSnapshot) => applyNodeChanges(changes, nodesSnapshot)),
    [],
  );
  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) =>
      setEdges((edgesSnapshot) => applyEdgeChanges(changes, edgesSnapshot)),
    [],
  );
  const onConnect = useCallback(
    (params: Connection) =>
      setEdges((edgesSnapshot) => addEdge(params, edgesSnapshot)),
    [],
  );

  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      setSelectedNodeId(node.id);
    },
    [setSelectedNodeId],
  );

  const onPaneClick = useCallback(() => {
    setSelectedNodeId(null);
  }, [setSelectedNodeId]);

  return (
    <ReactFlow
      proOptions={{ hideAttribution: true }}
      nodeTypes={nodeTypes}
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      onNodeClick={onNodeClick}
      onPaneClick={onPaneClick}
      fitView
    >
      <Background size={2} gap={20} bgColor="#f9f9f9" />
      <CanvasOverlay />
    </ReactFlow>
  );
}
