import {
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  Background,
  Connection,
  Edge,
  EdgeChange,
  Node,
  NodeChange,
  ReactFlow,
} from "@xyflow/react";
import React, { useCallback, useEffect, useRef, useState } from "react";

import { useWorkflowStore } from "@/stores/workflowStore";
import { NodeSelectItem } from "@/types/nodeSelectPanel";
import { NodeType } from "@/constants/enums";
import CanvasOverlay from "./canvasOverlay";
import GenericNode from "./node";

const nodeTypes = {
  genericNode: GenericNode,
};

export default function Workbench() {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  const setSelectedNodeId = useWorkflowStore(
    (state) => state.setSelectedNodeId,
  );
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);
  const addNode = useWorkflowStore((state) => state.addNode);

  useEffect(() => {
    if (currentWorkflow) {
      setNodes(
        currentWorkflow.nodes.map((node) => ({
          id: node.id,
          position: node.position,
          data: node.data,
          type: "genericNode",
        })),
      );
      setEdges(currentWorkflow.edges);
    }
  }, [currentWorkflow]);

  const onNodesChange = useCallback(
    (changes: NodeChange[]) =>
      setNodes((nodesSnapshot) => applyNodeChanges(changes, nodesSnapshot)),
    [],
  );
  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) =>
      setEdges((edgesSnapshot) => applyEdgeChanges(changes, edgesSnapshot)),
    [],
  );
  const onConnect = useCallback(
    (params: Connection) =>
      setEdges((edgesSnapshot) => addEdge(params, edgesSnapshot)),
    [],
  );

  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      setSelectedNodeId(node.id);
    },
    [setSelectedNodeId],
  );

  const onPaneClick = useCallback(() => {
    setSelectedNodeId(null);
  }, [setSelectedNodeId]);

  // 处理拖拽放置
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds) return;

      const type = event.dataTransfer.getData("application/reactflow");
      if (!type) return;

      try {
        const nodeData = JSON.parse(type) as {
          nodeType: string;
          nodeName: string;
          nodeData: NodeSelectItem;
        };

        // 计算放置位置
        const position = {
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        };

        // 添加节点
        addNode({
          label: nodeData.nodeName,
          type: nodeData.nodeType as NodeType,
          position,
          data: { label: nodeData.nodeName },
        });
      } catch (error) {
        console.error("Failed to parse drag data:", error);
      }
    },
    [addNode]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  return (
    <div ref={reactFlowWrapper} className="h-full w-full">
      <ReactFlow
        proOptions={{ hideAttribution: true }}
        nodeTypes={nodeTypes}
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onPaneClick={onPaneClick}
        onDrop={onDrop}
        onDragOver={onDragOver}
        fitView
      >
        <Background size={2} gap={20} bgColor="#f9f9f9" />
        <CanvasOverlay />
      </ReactFlow>
    </div>
  );
}
