import { Popover } from "antd";
import type { TooltipPlacement } from "antd/es/tooltip";
import { useState } from "react";

import NodeSelectPanel from "../nodeSelectPanel";
import { NodeSelectItem } from "@/types/nodeSelectPanel";

interface NodeSelectorProps {
  children: React.ReactNode;
  placement?: TooltipPlacement;
  onNodeSelect?: (node: NodeSelectItem) => void;
}

export default function NodeSelector({
  placement = "top",
  children,
  onNodeSelect,
}: NodeSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleNodeSelect = (node: NodeSelectItem) => {
    onNodeSelect?.(node);
    setOpen(false);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div>
      <Popover
        content={
          <NodeSelectPanel
            onNodeSelect={handleNodeSelect}
            onClose={handleClose}
          />
        }
        placement={placement}
        trigger={["click"]}
        arrow={false}
        open={open}
        onOpenChange={setOpen}

      >
        {children}
      </Popover>
    </div>
  );
}
