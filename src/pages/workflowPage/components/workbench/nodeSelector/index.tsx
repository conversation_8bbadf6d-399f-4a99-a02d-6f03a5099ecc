import { Popover } from "antd";
import type { TooltipPlacement } from "antd/es/tooltip";

import NodeSelectPanel from "../nodeSelectPanel";

interface NodeSelectorProps {
  children: React.ReactNode;
  placement?: TooltipPlacement;
}

export default function NodeSelector({
  placement = "top",
  children,
}: NodeSelectorProps) {
  return (
    <div>
      <Popover
        content={<NodeSelectPanel />}
        placement={placement}
        trigger={["click"]}
        arrow={false}
      >
        {children}
      </Popover>
    </div>
  );
}
