import { Dropdown, MenuProps } from "antd";

import { MoreIcon } from "@/components/main/icon";
import { cn } from "@/utils/utils";

interface NodeHeaderProps {
  label: string;
}

const menuItems: MenuProps["items"] = [
  { key: "copy", label: "复制" },
  { key: "delete", label: "删除" },
];

export default function NodeHeader({ label }: NodeHeaderProps) {
  return (
    <div className="flex items-center gap-2">
      <div
        className={cn(
          "flex h-6 w-6 items-center justify-center rounded-md bg-bg-primary-1",
        )}
      >
        N
      </div>
      <div className="flex-1 truncate text-sm font-medium leading-[22px]">
        {label}
      </div>
      <div onClick={(e) => e.stopPropagation()}>
        <Dropdown menu={{ items: menuItems }} overlayClassName="w-[100px]">
          <MoreIcon className="text-text-2" />
        </Dropdown>
      </div>
    </div>
  );
}
