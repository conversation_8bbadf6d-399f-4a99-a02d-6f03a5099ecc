import { Input } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";

import {
  filterNodesByKeyword,
  getCategoriesWithFilteredNodes,
  getRandomPlaceholder,
  NODE_CATEGORIES,
} from "@/constants/nodeSelectPanel";
import { NodeSelectItem, SearchState } from "@/types/nodeSelectPanel";
import { useWorkflowStore } from "@/stores/workflowStore";
import { useNodePosition } from "@/hooks/useNodePosition";

interface NodeSelectPanelProps {
  onNodeSelect?: (node: NodeSelectItem) => void;
  onClose?: () => void;
  onNodeDragStart?: (node: NodeSelectItem, event: React.DragEvent) => void;
}

export default function NodeSelectPanel({
  onNodeSelect,
  onClose,
  onNodeDragStart
}: NodeSelectPanelProps) {
  // 搜索状态
  const [searchState, setSearchState] = useState<SearchState>({
    keyword: "",
    filteredNodes: [],
    hasResults: true,
  });

  // 随机placeholder
  const [placeholder, setPlaceholder] = useState("");

  // 工作流存储
  const addNode = useWorkflowStore((state) => state.addNode);

  // 位置计算
  const { getRightSidePosition, getNearbyFreePosition } = useNodePosition();

  // 初始化placeholder
  useEffect(() => {
    setPlaceholder(getRandomPlaceholder());
  }, []);

  // 处理搜索输入
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const keyword = e.target.value;
    const filteredNodes = filterNodesByKeyword(keyword);

    setSearchState({
      keyword,
      filteredNodes,
      hasResults: filteredNodes.length > 0,
    });
  }, []);

  // Tab键自动填充placeholder
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Tab" && !searchState.keyword && placeholder) {
      e.preventDefault();
      // 从placeholder中提取关键词（去掉"搜索"、"输入"、"查找"等前缀和"..."后缀）
      const keyword = placeholder
        .replace(/^(搜索|输入|查找)/, "")
        .replace(/\.\.\.$/, "")
        .trim();

      if (keyword) {
        const filteredNodes = filterNodesByKeyword(keyword);
        setSearchState({
          keyword,
          filteredNodes,
          hasResults: filteredNodes.length > 0,
        });
      }
    }
  }, [searchState.keyword, placeholder]);

  // 获取要显示的分类数据
  const displayCategories = useMemo(() => {
    if (searchState.keyword) {
      return getCategoriesWithFilteredNodes(searchState.filteredNodes);
    }
    return NODE_CATEGORIES;
  }, [searchState.keyword, searchState.filteredNodes]);

  // 处理节点点击
  const handleNodeClick = useCallback((node: NodeSelectItem) => {
    // 调用外部回调
    onNodeSelect?.(node);

    // 计算节点添加位置（在画布右侧）
    const rightPosition = getRightSidePosition();
    const finalPosition = getNearbyFreePosition(rightPosition);

    // 添加节点到工作流
    addNode({
      label: node.name,
      type: node.type,
      position: finalPosition,
      data: { label: node.name },
    });

    // 关闭面板
    onClose?.();
  }, [onNodeSelect, addNode, onClose, getRightSidePosition, getNearbyFreePosition]);

  // 处理节点拖拽开始
  const handleNodeDragStart = useCallback((node: NodeSelectItem, event: React.DragEvent) => {
    // 设置拖拽数据
    event.dataTransfer.setData("application/reactflow", JSON.stringify({
      nodeType: node.type,
      nodeName: node.name,
      nodeData: node,
    }));
    event.dataTransfer.effectAllowed = "move";

    // 调用外部回调
    onNodeDragStart?.(node, event);
  }, [onNodeDragStart]);

  return (
    <div className="w-60 rounded-lg border border-border-1 bg-white shadow-lg animate-in fade-in-0 zoom-in-95 duration-200">
      {/* 搜索框 */}
      <div className="border-b border-border-1 p-3">
        <Input
          placeholder={placeholder}
          value={searchState.keyword}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          className="border-0 shadow-none focus:shadow-none hover:bg-gray-50 transition-colors"
          style={{ boxShadow: "none" }}
          autoFocus
        />
      </div>

      {/* 节点列表 */}
      <div className="max-h-80 overflow-y-auto p-2">
        {!searchState.hasResults && searchState.keyword ? (
          // 空状态
          <div className="flex flex-col items-center justify-center py-12 text-gray-400 animate-in fade-in-0 duration-300">
            <div className="mb-3 text-3xl opacity-60">🔍</div>
            <div className="text-sm font-medium text-gray-600 mb-1">未找到匹配的节点</div>
            <div className="text-xs text-gray-500">请尝试其他关键词</div>
          </div>
        ) : (
          // 分类列表
          displayCategories.map((category) => (
            <div key={category.key} className="mb-4 last:mb-0">
              {/* 分类标题 */}
              {category.showTitle && (
                <div className="mb-2 px-2 text-xs font-semibold text-gray-600 uppercase tracking-wide border-b border-gray-100 pb-1">
                  {category.label}
                </div>
              )}

              {/* 节点列表 */}
              <div className="space-y-1">
                {category.nodes.map((node) => (
                  <NodeItem
                    key={node.id}
                    node={node}
                    onClick={() => handleNodeClick(node)}
                    onDragStart={(event) => handleNodeDragStart(node, event)}
                  />
                ))}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

// 节点项组件
interface NodeItemProps {
  node: NodeSelectItem;
  onClick: () => void;
  onDragStart?: (event: React.DragEvent) => void;
}

function NodeItem({ node, onClick, onDragStart }: NodeItemProps) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (event: React.DragEvent) => {
    setIsDragging(true);
    onDragStart?.(event);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  return (
    <div
      className={`
        flex cursor-pointer items-center gap-3 rounded-md p-2 transition-all duration-200 select-none
        hover:bg-gray-50 hover:shadow-sm active:scale-95
        ${isDragging ? "opacity-50 scale-95" : ""}
        ${!node.enabled ? "opacity-50 cursor-not-allowed" : ""}
      `}
      onClick={node.enabled ? onClick : undefined}
      draggable={node.enabled}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {/* 节点图标 */}
      {node.icon && (
        <div className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110">
          {node.icon}
        </div>
      )}

      {/* 节点信息 */}
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-gray-900 truncate">
          {node.name}
        </div>
        {node.description && (
          <div className="text-xs text-gray-500 truncate">
            {node.description}
          </div>
        )}
      </div>

      {/* 拖拽提示 */}
      {node.enabled && (
        <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" className="text-gray-400">
            <path d="M3 2h1v1H3V2zm2 0h1v1H5V2zm2 0h1v1H7V2zM3 4h1v1H3V4zm2 0h1v1H5V4zm2 0h1v1H7V4zM3 6h1v1H3V6zm2 0h1v1H5V6zm2 0h1v1H7V6z"/>
          </svg>
        </div>
      )}
    </div>
  );
}