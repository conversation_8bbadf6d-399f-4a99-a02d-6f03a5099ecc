import { ReactFlowProvider } from "@xyflow/react";
import { useEffect } from "react";

import SubPageLayout from "@/components/main/layout/components/subPageLayout";
import { NodeType, WorkflowStatus } from "@/constants/enums";
import { useWorkflowStore } from "@/stores/workflowStore";
import { WorkflowData } from "@/types/workflow";
import HeaderControl from "./components/headerControl";
import Workbench from "./components/workbench";

const mockWorkflowData: WorkflowData = {
  id: "workflow-1",
  label: "工作流1",
  description: "工作流1",
  status: WorkflowStatus.DRAFT,
  nodes: [
    {
      id: "start",
      type: NodeType.START,
      label: "开始",
      position: { x: 300, y: 200 },
      data: { label: "开始" },
    },
    {
      id: "llm",
      type: NodeType.LLM,
      label: "LLM",
      position: { x: 580, y: 200 },
      data: { label: "LLM" },
    },
    {
      id: "end",
      type: NodeType.END,
      label: "结束",
      position: { x: 860, y: 200 },
      data: { label: "结束" },
    },
  ],
  edges: [
    {
      id: "start-llm",
      source: "start",
      target: "llm",
      sourceHandle: "",
      targetHandle: "",
    },
    {
      id: "llm-end",
      source: "llm",
      target: "end",
      sourceHandle: "",
      targetHandle: "",
    },
  ],
  updateTime: "",
  publishTime: "",
};

export default function WorkflowPage() {
  const setCurrentWorkflow = useWorkflowStore(
    (state) => state.setCurrentWorkflow,
  );

  useEffect(() => {
    setCurrentWorkflow(mockWorkflowData);
  }, [setCurrentWorkflow]);

  return (
    <ReactFlowProvider>
      <SubPageLayout
        title="工作流"
        backPath="/workflow"
        wrapperClassName="h-full bg-white rounded-lg border border-solid border-border-1 overflow-hidden"
        navRightContent={<HeaderControl />}
      >
        <Workbench />
      </SubPageLayout>
    </ReactFlowProvider>
  );
}
